import 'package:freezed_annotation/freezed_annotation.dart';
import '../user_model.dart';

part 'login_response.freezed.dart';
part 'login_response.g.dart';

/// API'den dönen login response modeli
/// API response: { "token": "string", "user": { "id": "string", "name": "string", "email": "string" } }
@freezed
class LoginResponse with _$LoginResponse {
  const factory LoginResponse({
    required String token,
    required UserModel user,
  }) = _LoginResponse;

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    // API response yapısı: { "response": {...}, "data": { "token": "...", "user": {...} } }
    final data = json['data'] as Map<String, dynamic>;

    // User data'sını al
    final userData = Map<String, dynamic>.from(data);
    userData.remove('token'); // token'ı user data'sından çıkar

    return LoginResponse(
      token: data['token'] as String,
      user: UserModel.fromJson(userData),
    );
  }
}
