/// App strings for SinFlix application
class AppStrings {
  AppStrings._();

  // Common
  static const String appName = 'SinFlix'; // TODO: Remove this hardcoded string
  static const String loading = 'Yükleniyor...'; // TODO: Remove this hardcoded string
  static const String error = 'Hata'; // TODO: Remove this hardcoded string
  static const String success = 'Başarılı'; // TODO: Remove this hardcoded string
  static const String cancel = 'İptal'; // TODO: Remove this hardcoded string
  static const String ok = 'Tamam'; // TODO: Remove this hardcoded string

  // Auth
  static const String welcome = 'Hoşgeldiniz'; // TODO: Remove this hardcoded string
  static const String welcomeSubtitle = 'Tempus varius a vitae interdum id tortor\nelementum tristique eleifend at.'; // TODO: Remove this hardcoded string
  
  // Login
  static const String login = 'Giriş Yap'; // TODO: Remove this hardcoded string
  static const String email = 'E-Posta'; // TODO: Remove this hardcoded string
  static const String password = '<PERSON>ifre'; // TODO: Remove this hardcoded string
  static const String forgotPassword = 'Şifremi Unuttum'; // TODO: Remove this hardcoded string
  static const String dontHaveAccount = 'Hesabın yok mu? '; // TODO: Remove this hardcoded string
  static const String signUp = 'Kaydol'; // TODO: Remove this hardcoded string
  static const String loginWithGoogle = 'Google ile Giriş'; // TODO: Remove this hardcoded string
  static const String loginWithApple = 'Apple ile Giriş'; // TODO: Remove this hardcoded string
  static const String loginWithFacebook = 'Facebook ile Giriş'; // TODO: Remove this hardcoded string

  // Register
  static const String register = 'Kaydol'; // TODO: Remove this hardcoded string
  static const String registerNow = 'Şimdi Kaydol'; // TODO: Remove this hardcoded string
  static const String fullName = 'Ad Soyad'; // TODO: Remove this hardcoded string
  static const String confirmPassword = 'Şifre Tekrar'; // TODO: Remove this hardcoded string
  static const String acceptTerms = 'Kullanıcı sözleşmesini '; // TODO: Remove this hardcoded string
  static const String readAndAccept = 'okudum ve kabul ediyorum.'; // TODO: Remove this hardcoded string
  static const String termsCondition = ' Bu sözleşmeyi okuyarak devam ediniz lütfen.'; // TODO: Remove this hardcoded string
  static const String alreadyHaveAccount = 'Zaten bir hesabın var mı? '; // TODO: Remove this hardcoded string
  static const String loginExclamation = 'Giriş Yap!'; // TODO: Remove this hardcoded string

  // Validation Messages
  static const String emailRequired = 'E-posta gereklidir'; // TODO: Remove this hardcoded string
  static const String emailInvalid = 'Geçerli bir e-posta adresi girin'; // TODO: Remove this hardcoded string
  static const String passwordRequired = 'Şifre gereklidir'; // TODO: Remove this hardcoded string
  static const String passwordMinLength = 'Şifre en az 6 karakter olmalıdır'; // TODO: Remove this hardcoded string
  static const String fullNameRequired = 'Ad Soyad gereklidir'; // TODO: Remove this hardcoded string
  static const String confirmPasswordRequired = 'Şifre tekrarı gereklidir'; // TODO: Remove this hardcoded string
  static const String passwordsNotMatch = 'Şifreler eşleşmiyor'; // TODO: Remove this hardcoded string
  static const String termsRequired = 'Kullanım şartlarını kabul etmelisiniz'; // TODO: Remove this hardcoded string

  // Home
  static const String home = 'Ana Sayfa'; // TODO: Remove this hardcoded string
  static const String movies = 'Filmler'; // TODO: Remove this hardcoded string
  static const String favorites = 'Favoriler'; // TODO: Remove this hardcoded string
  static const String profile = 'Profil'; // TODO: Remove this hardcoded string
  static const String search = 'Ara'; // TODO: Remove this hardcoded string
}
