// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
// import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, TargetPlatform;

/// Mock FirebaseOptions class - Firebase temporarily disabled
class FirebaseOptions {
  const FirebaseOptions({
    required this.apiKey,
    required this.appId,
    required this.messagingSenderId,
    required this.projectId,
    this.storageBucket,
    this.iosBundleId,
  });

  final String apiKey;
  final String appId;
  final String messagingSenderId;
  final String projectId;
  final String? storageBucket;
  final String? iosBundleId;
}

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCF3bKzuJd8QQLYSrSEDYkfC0bJ2KfElF4',
    appId: '1:698869096549:android:c2ee1527149bfd505ca68c',
    messagingSenderId: '698869096549',
    projectId: 'sinflix-9f092',
    storageBucket: 'sinflix-9f092.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDxQr6V4qDKVd-bdnrETCigvPY3GTkK2PM',
    appId: '1:698869096549:ios:273eca2508f9a4f55ca68c',
    messagingSenderId: '698869096549',
    projectId: 'sinflix-9f092',
    storageBucket: 'sinflix-9f092.firebasestorage.app',
    iosBundleId: 'com.sinflix.app',
  );
}
