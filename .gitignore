# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# VSCode related
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
.flutter-plugins-dependencies

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Android related
android/app/google-services.json
android/key.properties
android/app/upload-keystore.jks
android/app/release-keystore.jks
android/.gradle/
android/local.properties
android/gradlew
android/gradlew.bat
android/gradle/wrapper/gradle-wrapper.jar

# iOS related
ios/Runner/GoogleService-Info.plist
ios/firebase_app_id_file.json
ios/Runner/GeneratedPluginRegistrant.*
ios/Flutter/flutter_export_environment.sh
ios/Pods/
ios/.symlinks/
ios/Flutter/App.framework
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
ios/ServiceDefinitions.json
ios/Runner/Runner-Bridging-Header.h

# Firebase related
.firebase/
firebase-debug.log
firebase-debug.*.log
.firebaserc
firebase.json
firestore.rules
firestore.indexes.json
storage.rules
database.rules

# Environment variables
.env
.env.local
.env.*.local

# Coverage
coverage/
lcov.info

# Generated files
*.g.dart
*.freezed.dart
*.gr.dart
*.config.dart

# Localization
*.arb

# Test related
test/coverage/
integration_test/screenshots/